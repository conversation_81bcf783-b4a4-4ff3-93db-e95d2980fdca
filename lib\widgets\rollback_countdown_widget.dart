import 'dart:async';
import 'package:flutter/material.dart';

class RollbackCountdownWidget extends StatefulWidget {
  final Duration initialTimeRemaining;
  final int remainingCount;
  final VoidCallback? onTimeExpired;

  const RollbackCountdownWidget({
    Key? key,
    required this.initialTimeRemaining,
    required this.remainingCount,
    this.onTimeExpired,
  }) : super(key: key);

  @override
  State<RollbackCountdownWidget> createState() => _RollbackCountdownWidgetState();
}

class _RollbackCountdownWidgetState extends State<RollbackCountdownWidget> {
  late Duration _timeRemaining;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _timeRemaining = widget.initialTimeRemaining;
    _startCountdown();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeRemaining.inSeconds <= 0) {
        timer.cancel();
        widget.onTimeExpired?.call();
        return;
      }

      setState(() {
        _timeRemaining = Duration(seconds: _timeRemaining.inSeconds - 1);
      });
    });
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  Color _getTimeColor() {
    final totalHours = widget.initialTimeRemaining.inHours;
    final remainingHours = _timeRemaining.inHours;
    
    if (remainingHours <= 1) {
      return Colors.red[600]!;
    } else if (remainingHours <= 6) {
      return Colors.orange[600]!;
    } else {
      return Colors.green[600]!;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_timeRemaining.inSeconds <= 0) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          children: [
            Icon(
              Icons.timer_off,
              color: Colors.grey[600],
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '回溯时间已过期',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getTimeColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getTimeColor().withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                color: _getTimeColor(),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '回溯倒计时',
                style: TextStyle(
                  color: _getTimeColor(),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getTimeColor(),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _formatDuration(_timeRemaining),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.undo,
                color: _getTimeColor(),
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                '剩余回溯机会：${widget.remainingCount} 次',
                style: TextStyle(
                  color: _getTimeColor(),
                  fontSize: 12,
                ),
              ),
            ],
          ),
          if (_timeRemaining.inHours <= 1) ...[
            const SizedBox(height: 4),
            Text(
              '⚠️ 时间即将到期，请尽快操作！',
              style: TextStyle(
                color: Colors.red[600],
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class RollbackInfoCard extends StatelessWidget {
  final Duration timeRemaining;
  final int remainingCount;
  final bool canRollback;

  const RollbackInfoCard({
    Key? key,
    required this.timeRemaining,
    required this.remainingCount,
    required this.canRollback,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!canRollback) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '回溯信息',
                  style: TextStyle(
                    color: Colors.blue[800],
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.undo,
                    color: Colors.orange[600],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '剩余回溯机会：$remainingCount 次',
                      style: TextStyle(
                        color: Colors.orange[800],
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '当前工单状态为"已解决"，您可以回溯一次到"处理中"状态。',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
