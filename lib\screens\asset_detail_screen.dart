import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../providers/asset_provider.dart';
import '../models/asset.dart';
import '../config/routes.dart';
import '../widgets/main_layout.dart';
import '../widgets/success_animation.dart';
import '../utils/timezone_utils.dart';

class AssetDetailScreen extends StatefulWidget {
  final String assetId;
  
  const AssetDetailScreen({
    super.key,
    required this.assetId,
  });

  @override
  State<AssetDetailScreen> createState() => _AssetDetailScreenState();
}

class _AssetDetailScreenState extends State<AssetDetailScreen> {
  Asset? _asset;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAsset();
  }

  Future<void> _loadAsset() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final assetProvider = Provider.of<AssetProvider>(context, listen: false);
      final asset = await assetProvider.getAsset(widget.assetId);
      
      if (mounted) {
        setState(() {
          _asset = asset;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteAsset() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.confirmDelete),
        content: Text(AppLocalizations.of(context)!.confirmDeleteAsset(_asset?.name ?? '')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(AppLocalizations.of(context)!.delete),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final assetProvider = Provider.of<AssetProvider>(context, listen: false);
      final success = await assetProvider.deleteAsset(widget.assetId);

      if (mounted) {
        if (success) {
          // 显示现代化的成功动画
          SuccessAnimationOverlay.show(
            context,
            title: AppLocalizations.of(context)!.deleteSuccess,
            message: AppLocalizations.of(context)!.assetDeletedSuccess,
            onComplete: () {
              // 成功后返回资产列表并强制刷新
              context.go(AppRoutes.assetList);
              // 延迟一下再刷新，确保页面已经加载
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  final assetProvider = Provider.of<AssetProvider>(context, listen: false);
                  assetProvider.refreshAssets();
                }
              });
            },
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)!.deleteFailed(assetProvider.errorMessage ?? '')),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  String _getCategoryDisplayName(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return AppLocalizations.of(context)!.categoryLaptop;
      case AssetCategory.desktop:
        return AppLocalizations.of(context)!.categoryDesktop;
      case AssetCategory.monitor:
        return AppLocalizations.of(context)!.categoryMonitor;
      case AssetCategory.printer:
        return AppLocalizations.of(context)!.categoryPrinter;
      case AssetCategory.phone:
        return AppLocalizations.of(context)!.categoryMobile;
      case AssetCategory.tablet:
        return AppLocalizations.of(context)!.categoryTablet;
      case AssetCategory.server:
        return AppLocalizations.of(context)!.categoryServer;
      case AssetCategory.network:
        return AppLocalizations.of(context)!.categoryNetwork;
      case AssetCategory.other:
        return AppLocalizations.of(context)!.categoryOther;
    }
  }

  String _getStatusDisplayName(AssetStatus status) {
    switch (status) {
      case AssetStatus.available:
        return AppLocalizations.of(context)!.statusUnassigned;
      case AssetStatus.assigned:
        return AppLocalizations.of(context)!.statusAssigned;
      case AssetStatus.maintenance:
        return AppLocalizations.of(context)!.statusMaintenance;
      case AssetStatus.retired:
        return AppLocalizations.of(context)!.statusRetired;
    }
  }

  Color _getStatusColor(AssetStatus status) {
    switch (status) {
      case AssetStatus.available:
        return Colors.green;
      case AssetStatus.assigned:
        return Colors.blue;
      case AssetStatus.maintenance:
        return Colors.orange;
      case AssetStatus.retired:
        return Colors.red;
    }
  }

  IconData _getCategoryIcon(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return FontAwesomeIcons.laptop;
      case AssetCategory.desktop:
        return FontAwesomeIcons.desktop;
      case AssetCategory.monitor:
        return FontAwesomeIcons.tv;
      case AssetCategory.printer:
        return FontAwesomeIcons.print;
      case AssetCategory.phone:
        return FontAwesomeIcons.mobileScreen;
      case AssetCategory.tablet:
        return FontAwesomeIcons.tablet;
      case AssetCategory.server:
        return FontAwesomeIcons.server;
      case AssetCategory.network:
        return FontAwesomeIcons.wifi;
      case AssetCategory.other:
        return FontAwesomeIcons.cube;
    }
  }

  Widget _buildInfoCard(String title, String? value, {IconData? icon}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (icon != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: Colors.blue[600],
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  value ?? AppLocalizations.of(context)!.notSet,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(4, 24, 4, 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w700,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildModernChip(String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2), width: 1),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 14),
          const SizedBox(width: 6),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(AssetStatus status) {
    Color color;
    IconData icon;
    String displayName = _getStatusDisplayName(status);

    switch (status) {
      case AssetStatus.available:
        color = Colors.green;
        icon = Icons.check_circle_rounded;
        break;
      case AssetStatus.assigned:
        color = Colors.blue;
        icon = Icons.person_rounded;
        break;
      case AssetStatus.maintenance:
        color = Colors.orange;
        icon = Icons.build_rounded;
        break;
      case AssetStatus.retired:
        color = Colors.red;
        icon = Icons.delete_forever_rounded;
        break;
    }

    return _buildModernChip(displayName, color, icon);
  }

  Color _getCategoryColor(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return Colors.blue;
      case AssetCategory.desktop:
        return Colors.indigo;
      case AssetCategory.monitor:
        return Colors.purple;
      case AssetCategory.printer:
        return Colors.teal;
      case AssetCategory.phone:
        return Colors.orange;
      case AssetCategory.tablet:
        return Colors.pink;
      case AssetCategory.server:
        return Colors.red;
      case AssetCategory.network:
        return Colors.green;
      case AssetCategory.other:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return TimezoneUtils.formatDateTime(dateTime);
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      currentRoute: '/assets/${widget.assetId}',
      showBackButton: true,
      title: AppLocalizations.of(context)!.assetDetails,
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        AppLocalizations.of(context)!.loadFailed,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _errorMessage!,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadAsset,
                        child: Text(AppLocalizations.of(context)!.retry),
                      ),
                    ],
                  ),
                )
              : _asset == null
                  ? Center(
                      child: Text(AppLocalizations.of(context)!.assetNotExists),
                    )
                  : RefreshIndicator(
                      onRefresh: _loadAsset,
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 现代化头部区域
                            Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    _getStatusColor(_asset!.status).withOpacity(0.1),
                                    _getStatusColor(_asset!.status).withOpacity(0.05),
                                  ],
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(24),
                                child: Column(
                                  children: [
                                    // 顶部操作栏
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius: BorderRadius.circular(12),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withOpacity(0.1),
                                                spreadRadius: 0,
                                                blurRadius: 8,
                                                offset: const Offset(0, 2),
                                              ),
                                            ],
                                          ),
                                          child: IconButton(
                                            icon: Icon(Icons.edit_rounded, color: Colors.blue[600]),
                                            onPressed: () {
                                              context.go('/assets/${widget.assetId}/edit');
                                            },
                                            tooltip: AppLocalizations.of(context)!.editAsset,
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius: BorderRadius.circular(12),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withOpacity(0.1),
                                                spreadRadius: 0,
                                                blurRadius: 8,
                                                offset: const Offset(0, 2),
                                              ),
                                            ],
                                          ),
                                          child: PopupMenuButton<String>(
                                            icon: Icon(Icons.more_vert_rounded, color: Colors.grey[600]),
                                            onSelected: (value) {
                                              switch (value) {
                                                case 'delete':
                                                  _deleteAsset();
                                                  break;
                                              }
                                            },
                                            itemBuilder: (context) => [
                                              PopupMenuItem(
                                                value: 'delete',
                                                child: Row(
                                                  children: [
                                                    const Icon(Icons.delete_rounded, color: Colors.red),
                                                    const SizedBox(width: 8),
                                                    Text(AppLocalizations.of(context)!.delete, style: const TextStyle(color: Colors.red)),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),

                                    const SizedBox(height: 24),

                                    // 资产头部信息
                                    Container(
                                      width: 100,
                                      height: 100,
                                      decoration: BoxDecoration(
                                        color: _getStatusColor(_asset!.status),
                                        borderRadius: BorderRadius.circular(24),
                                        boxShadow: [
                                          BoxShadow(
                                            color: _getStatusColor(_asset!.status).withOpacity(0.3),
                                            spreadRadius: 0,
                                            blurRadius: 20,
                                            offset: const Offset(0, 8),
                                          ),
                                        ],
                                      ),
                                      child: Center(
                                        child: FaIcon(
                                          _getCategoryIcon(_asset!.category),
                                          color: Colors.white,
                                          size: 40,
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 20),

                                    Text(
                                      _asset!.name,
                                      style: const TextStyle(
                                        fontSize: 28,
                                        fontWeight: FontWeight.w800,
                                        color: Colors.black87,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),

                                    const SizedBox(height: 12),

                                    Text(
                                      _asset!.assetNumber,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.grey[600],
                                      ),
                                      textAlign: TextAlign.center,
                                    ),

                                    const SizedBox(height: 16),

                                    // 标签行
                                    Wrap(
                                      spacing: 12,
                                      runSpacing: 8,
                                      alignment: WrapAlignment.center,
                                      children: [
                                        _buildStatusChip(_asset!.status),
                                        _buildModernChip(
                                          _getCategoryDisplayName(_asset!.category),
                                          _getCategoryColor(_asset!.category),
                                          _getCategoryIcon(_asset!.category),
                                        ),
                                        if (_asset!.assignedTo != null && _asset!.assignedTo!.isNotEmpty)
                                          _buildModernChip(
                                            _asset!.assignedTo!,
                                            Colors.indigo,
                                            Icons.person_rounded,
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            // 内容区域
                            Padding(
                              padding: const EdgeInsets.all(20),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // 基本信息
                                  _buildSectionTitle(AppLocalizations.of(context)!.basicInfo),
                                  _buildInfoCard(AppLocalizations.of(context)!.assetNumber, _asset!.assetNumber, icon: Icons.qr_code_rounded),
                                  _buildInfoCard(AppLocalizations.of(context)!.category, _getCategoryDisplayName(_asset!.category), icon: Icons.category_rounded),
                                  _buildInfoCard(AppLocalizations.of(context)!.brand, _asset!.brand, icon: Icons.business_rounded),
                                  _buildInfoCard(AppLocalizations.of(context)!.model, _asset!.model, icon: Icons.info_rounded),
                                  if (_asset!.serialNumber != null)
                                    _buildInfoCard(AppLocalizations.of(context)!.serialNumber, _asset!.serialNumber, icon: Icons.tag_rounded),

                                  // 财务信息
                                  if (_asset!.value != null || _asset!.purchaseDate != null || _asset!.vendor != null) ...[
                                    _buildSectionTitle(AppLocalizations.of(context)!.financialInfo),
                                    if (_asset!.value != null)
                                      _buildInfoCard(AppLocalizations.of(context)!.value, '¥${_asset!.value!.toStringAsFixed(2)}', icon: Icons.attach_money_rounded),
                                    if (_asset!.purchaseDate != null)
                                      _buildInfoCard(AppLocalizations.of(context)!.purchaseDate, TimezoneUtils.formatDate(_asset!.purchaseDate!), icon: Icons.calendar_today_rounded),
                                    if (_asset!.vendor != null)
                                      _buildInfoCard(AppLocalizations.of(context)!.vendor, _asset!.vendor, icon: Icons.store_rounded),
                                  ],

                                  // 分配信息
                                  _buildSectionTitle(AppLocalizations.of(context)!.assignmentInfo),
                                  _buildInfoCard(AppLocalizations.of(context)!.assignedTo, _asset!.assignedTo, icon: Icons.person_rounded),
                                  if (_asset!.location != null)
                                    _buildInfoCard(AppLocalizations.of(context)!.location, _asset!.location, icon: Icons.location_on_rounded),

                                  // 维护信息
                                  if (_asset!.lastMaintenanceDate != null || _asset!.nextMaintenanceDate != null) ...[
                                    _buildSectionTitle(AppLocalizations.of(context)!.maintenanceInfo),
                                    if (_asset!.lastMaintenanceDate != null)
                                      _buildInfoCard(AppLocalizations.of(context)!.lastMaintenanceDate, TimezoneUtils.formatDate(_asset!.lastMaintenanceDate!), icon: Icons.build_rounded),
                                    if (_asset!.nextMaintenanceDate != null)
                                      _buildInfoCard(AppLocalizations.of(context)!.nextMaintenanceDate, TimezoneUtils.formatDate(_asset!.nextMaintenanceDate!), icon: Icons.schedule_rounded),
                                  ],

                                  // 系统信息
                                  _buildSectionTitle(AppLocalizations.of(context)!.systemInfo),
                                  if (_asset!.createdAt != null)
                                    _buildInfoCard(AppLocalizations.of(context)!.createdAt, _formatDateTime(_asset!.createdAt!), icon: Icons.add_circle_rounded),
                                  if (_asset!.updatedAt != null)
                                    _buildInfoCard(AppLocalizations.of(context)!.updatedAt, _formatDateTime(_asset!.updatedAt!), icon: Icons.update_rounded),

                                  // 描述信息
                                  if (_asset!.description != null) ...[
                                    _buildSectionTitle(AppLocalizations.of(context)!.description),
                                    Container(
                                      width: double.infinity,
                                      padding: const EdgeInsets.all(20),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(16),
                                        border: Border.all(color: Colors.grey[200]!, width: 1),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withOpacity(0.02),
                                            spreadRadius: 0,
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Text(
                                        _asset!.description!,
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.black87,
                                          height: 1.5,
                                        ),
                                      ),
                                    ),
                                  ],

                                  const SizedBox(height: 80),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
    );
  }
}
