using Microsoft.EntityFrameworkCore;
using ITAssetAPI.Data;
using ITAssetAPI.Models;
using ITAssetAPI.DTOs;

namespace ITAssetAPI.Services
{
    public class TicketService
    {
        private readonly ApplicationDbContext _context;

        public TicketService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<TicketListDto>> GetUserTicketsAsync(int userId)
        {
            var tickets = await _context.Tickets
                .Where(t => t.UserId == userId)
                .OrderByDescending(t => t.CreatedDate)
                .Select(t => new TicketListDto
                {
                    Id = t.Id,
                    TicketNumber = t.TicketNumber,
                    Title = t.Title,
                    Description = t.Description,
                    Status = t.Status.ToString(),
                    Priority = t.Priority.ToString(),
                    Category = t.Category.ToString(),
                    UserName = t.UserName,
                    AssignedToName = t.AssignedToName,
                    CreatedDate = t.CreatedDate,
                    UpdatedDate = t.UpdatedDate,
                    AssignedDate = t.AssignedDate
                })
                .ToListAsync();

            return tickets;
        }

        public async Task<List<TicketListDto>> GetAllTicketsAsync()
        {
            var tickets = await _context.Tickets
                .OrderByDescending(t => t.CreatedDate)
                .Select(t => new TicketListDto
                {
                    Id = t.Id,
                    TicketNumber = t.TicketNumber,
                    Title = t.Title,
                    Description = t.Description,
                    Status = t.Status.ToString(),
                    Priority = t.Priority.ToString(),
                    Category = t.Category.ToString(),
                    UserName = t.UserName,
                    AssignedToName = t.AssignedToName,
                    CreatedDate = t.CreatedDate,
                    UpdatedDate = t.UpdatedDate,
                    AssignedDate = t.AssignedDate
                })
                .ToListAsync();

            return tickets;
        }

        public async Task<List<TicketListDto>> GetTicketsWithUnreadStatusAsync(int userId)
        {
            var tickets = await _context.Tickets
                .OrderByDescending(t => t.CreatedDate)
                .ToListAsync();

            var ticketDtos = new List<TicketListDto>();

            foreach (var ticket in tickets)
            {
                var unreadCount = await GetUnreadCommentsCountAsync(ticket.Id, userId);

                ticketDtos.Add(new TicketListDto
                {
                    Id = ticket.Id,
                    TicketNumber = ticket.TicketNumber,
                    Title = ticket.Title,
                    Description = ticket.Description,
                    Status = ticket.Status.ToString(),
                    Priority = ticket.Priority.ToString(),
                    Category = ticket.Category.ToString(),
                    UserName = ticket.UserName,
                    AssignedToName = ticket.AssignedToName,
                    CreatedDate = ticket.CreatedDate,
                    UpdatedDate = ticket.UpdatedDate,
                    AssignedDate = ticket.AssignedDate,
                    UnreadCommentsCount = unreadCount,
                    HasUnreadComments = unreadCount > 0
                });
            }

            return ticketDtos;
        }

        public async Task<TicketDto?> GetTicketByIdAsync(int id)
        {
            var ticket = await _context.Tickets
                .Include(t => t.Comments)
                .Include(t => t.Attachments)
                .FirstOrDefaultAsync(t => t.Id == id);

            if (ticket == null)
                return null;

            return new TicketDto
            {
                Id = ticket.Id,
                TicketNumber = ticket.TicketNumber,
                Title = ticket.Title,
                Description = ticket.Description,
                Status = ticket.Status.ToString(),
                Priority = ticket.Priority.ToString(),
                Category = ticket.Category.ToString(),
                UserId = ticket.UserId,
                UserName = ticket.UserName,
                AssignedToId = ticket.AssignedToId,
                AssignedToName = ticket.AssignedToName,
                CreatedDate = ticket.CreatedDate,
                UpdatedDate = ticket.UpdatedDate,
                AssignedDate = ticket.AssignedDate,
                ResolvedDate = ticket.ResolvedDate,
                ClosedDate = ticket.ClosedDate,
                LastStatusChangeDate = ticket.LastStatusChangeDate,
                PreviousStatus = ticket.PreviousStatus,
                RollbackCount = ticket.RollbackCount,
                LastRollbackDate = ticket.LastRollbackDate,
                Comments = ticket.Comments.Select(c => new TicketCommentDto
                {
                    Id = c.Id,
                    TicketId = c.TicketId,
                    UserId = c.UserId,
                    UserName = c.UserName,
                    UserAvatarUrl = c.UserAvatarUrl,
                    Content = c.Content,
                    IsInternal = c.IsInternal,
                    CreatedDate = c.CreatedDate
                }).ToList(),
                Attachments = ticket.Attachments.Select(a => new TicketAttachmentDto
                {
                    Id = a.Id,
                    TicketId = a.TicketId,
                    FileName = a.FileName,
                    FilePath = a.FilePath,
                    FileSize = a.FileSize,
                    ContentType = a.ContentType,
                    UploadedDate = a.UploadedDate
                }).ToList()
            };
        }

        public async Task<TicketDto> CreateTicketAsync(CreateTicketDto createTicketDto, int userId, string userName)
        {
            // Parse enums
            if (!Enum.TryParse<TicketPriority>(createTicketDto.Priority, out var priority))
                priority = TicketPriority.Medium;

            if (!Enum.TryParse<TicketCategory>(createTicketDto.Category, out var category))
                category = TicketCategory.Other;

            var ticket = new Ticket
            {
                Title = createTicketDto.Title,
                Description = createTicketDto.Description,
                Priority = priority,
                Category = category,
                UserId = userId,
                UserName = userName,
                Status = TicketStatus.Pending,
                CreatedDate = DateTime.UtcNow
            };

            _context.Tickets.Add(ticket);
            await _context.SaveChangesAsync();

            // Generate ticket number after saving to get the ID
            ticket.TicketNumber = $"TKT{ticket.Id:D5}";
            await _context.SaveChangesAsync();

            return new TicketDto
            {
                Id = ticket.Id,
                TicketNumber = ticket.TicketNumber,
                Title = ticket.Title,
                Description = ticket.Description,
                Status = ticket.Status.ToString(),
                Priority = ticket.Priority.ToString(),
                Category = ticket.Category.ToString(),
                UserId = ticket.UserId,
                UserName = ticket.UserName,
                CreatedDate = ticket.CreatedDate,
                Comments = new List<TicketCommentDto>(),
                Attachments = new List<TicketAttachmentDto>()
            };
        }

        public async Task<bool> UpdateTicketAsync(int id, UpdateTicketDto updateTicketDto)
        {
            var ticket = await _context.Tickets.FindAsync(id);
            if (ticket == null)
                return false;

            // 检查状态切换限制
            if (!string.IsNullOrEmpty(updateTicketDto.Status) &&
                Enum.TryParse<TicketStatus>(updateTicketDto.Status, out var newStatus))
            {
                var validationResult = await ValidateStatusChangeAsync(ticket, newStatus);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException(validationResult.ErrorMessage);
                }
            }

            if (!string.IsNullOrEmpty(updateTicketDto.Title))
                ticket.Title = updateTicketDto.Title;

            if (!string.IsNullOrEmpty(updateTicketDto.Description))
                ticket.Description = updateTicketDto.Description;

            if (!string.IsNullOrEmpty(updateTicketDto.Status) &&
                Enum.TryParse<TicketStatus>(updateTicketDto.Status, out var status))
            {
                await UpdateTicketStatusAsync(ticket, status);
            }

            if (!string.IsNullOrEmpty(updateTicketDto.Priority) &&
                Enum.TryParse<TicketPriority>(updateTicketDto.Priority, out var priority))
                ticket.Priority = priority;

            if (!string.IsNullOrEmpty(updateTicketDto.Category) &&
                Enum.TryParse<TicketCategory>(updateTicketDto.Category, out var category))
                ticket.Category = category;

            if (updateTicketDto.AssignedToId.HasValue)
            {
                var assignedUser = await _context.Users.FindAsync(updateTicketDto.AssignedToId.Value);
                if (assignedUser != null)
                {
                    // Only set assigned date if it's a new assignment
                    if (ticket.AssignedToId != updateTicketDto.AssignedToId.Value)
                    {
                        ticket.AssignedDate = DateTime.UtcNow;
                    }
                    ticket.AssignedToId = updateTicketDto.AssignedToId.Value;
                    ticket.AssignedToName = assignedUser.FullName ?? assignedUser.Username;
                }
            }

            ticket.UpdatedDate = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> AddCommentAsync(int ticketId, AddCommentDto addCommentDto, int userId, string userName)
        {
            var ticket = await _context.Tickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            // Get user avatar URL
            var user = await _context.Users.FindAsync(userId);
            var userAvatarUrl = user?.AvatarUrl;

            var comment = new TicketComment
            {
                TicketId = ticketId,
                UserId = userId,
                UserName = userName,
                UserAvatarUrl = userAvatarUrl,
                Content = addCommentDto.Content,
                IsInternal = addCommentDto.IsInternal,
                CreatedDate = DateTime.UtcNow
            };

            _context.TicketComments.Add(comment);

            // Update ticket's UpdatedDate
            ticket.UpdatedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteCommentAsync(int commentId, int userId, bool isAdmin)
        {
            var comment = await _context.TicketComments.FindAsync(commentId);
            if (comment == null)
                return false;

            // Check if user can delete this comment
            // Users can only delete their own comments, admins can delete any comment
            if (!isAdmin && comment.UserId != userId)
                return false;

            _context.TicketComments.Remove(comment);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<int> BatchDeleteCommentsAsync(List<int> commentIds, int userId, bool isAdmin)
        {
            var comments = await _context.TicketComments
                .Where(c => commentIds.Contains(c.Id))
                .ToListAsync();

            var deletableComments = comments.Where(c => isAdmin || c.UserId == userId).ToList();

            if (deletableComments.Any())
            {
                _context.TicketComments.RemoveRange(deletableComments);
                await _context.SaveChangesAsync();
            }

            return deletableComments.Count;
        }

        public async Task<bool> DeleteTicketAsync(int id, int userId, bool isAdmin)
        {
            var ticket = await _context.Tickets.FindAsync(id);
            if (ticket == null)
                return false;

            // Check if user can delete this ticket
            // Only pending tickets can be deleted
            // Only the creator or admin can delete
            if (!string.Equals(ticket.Status.ToString(), "Pending", StringComparison.OrdinalIgnoreCase))
                return false;

            if (!isAdmin && ticket.UserId != userId)
                return false;

            _context.Tickets.Remove(ticket);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<int> GetUnreadCommentsCountAsync(int ticketId, int userId)
        {
            var viewRecord = await _context.TicketViewRecords
                .FirstOrDefaultAsync(vr => vr.TicketId == ticketId && vr.UserId == userId);

            var lastViewedAt = viewRecord?.LastViewedAt ?? DateTime.MinValue;

            var unreadCount = await _context.TicketComments
                .Where(c => c.TicketId == ticketId &&
                           c.CreatedDate > lastViewedAt &&
                           c.UserId != userId) // 不计算自己的评论
                .CountAsync();

            return unreadCount;
        }

        public async Task MarkTicketAsViewedAsync(int ticketId, int userId)
        {
            var viewRecord = await _context.TicketViewRecords
                .FirstOrDefaultAsync(vr => vr.TicketId == ticketId && vr.UserId == userId);

            if (viewRecord == null)
            {
                viewRecord = new TicketViewRecord
                {
                    TicketId = ticketId,
                    UserId = userId,
                    LastViewedAt = DateTime.UtcNow
                };
                _context.TicketViewRecords.Add(viewRecord);
            }
            else
            {
                viewRecord.LastViewedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
        }

        // 状态切换验证
        private async Task<StatusChangeValidationResult> ValidateStatusChangeAsync(Ticket ticket, TicketStatus newStatus)
        {
            var currentStatus = ticket.Status;

            // 规则1: 已关闭的工单不能切换到任何其他状态
            if (currentStatus == TicketStatus.Closed)
            {
                return new StatusChangeValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "已关闭的工单不能再切换状态"
                };
            }

            // 规则2: 从已解决回溯到处理中的限制（只检查次数）
            if (currentStatus == TicketStatus.Resolved && newStatus == TicketStatus.InProgress)
            {
                // 检查回溯次数限制
                if (ticket.RollbackCount >= 1)
                {
                    return new StatusChangeValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "已使用过一次回溯机会，无法再次回溯"
                    };
                }
            }

            return new StatusChangeValidationResult { IsValid = true };
        }

        // 更新工单状态
        private async Task UpdateTicketStatusAsync(Ticket ticket, TicketStatus newStatus)
        {
            var oldStatus = ticket.Status;

            // 记录状态变更
            ticket.PreviousStatus = oldStatus.ToString();
            ticket.LastStatusChangeDate = DateTime.UtcNow;
            ticket.Status = newStatus;

            // 根据新状态设置相应的时间戳
            switch (newStatus)
            {
                case TicketStatus.Resolved:
                    ticket.ResolvedDate = DateTime.UtcNow;
                    break;
                case TicketStatus.Closed:
                    ticket.ClosedDate = DateTime.UtcNow;
                    break;
                case TicketStatus.InProgress:
                    // 如果是从已解决回溯到处理中，记录回溯信息
                    if (oldStatus == TicketStatus.Resolved)
                    {
                        ticket.RollbackCount++;
                        ticket.LastRollbackDate = DateTime.UtcNow;
                        ticket.ResolvedDate = null; // 清除解决时间
                    }
                    break;
            }
        }

        // 获取工单状态限制信息
        public async Task<TicketStatusLimitsDto> GetTicketStatusLimitsAsync(int ticketId)
        {
            var ticket = await _context.Tickets.FindAsync(ticketId);
            if (ticket == null)
                return null;

            var limits = new TicketStatusLimitsDto
            {
                CanChangeStatus = ticket.Status != TicketStatus.Closed,
                CanRollback = false,
                RemainingRollbackCount = 0,
                RollbackTimeRemainingMilliseconds = 0
            };

            // 如果当前状态是已解决，检查回溯限制（只检查次数，不检查时间）
            if (ticket.Status == TicketStatus.Resolved && ticket.RollbackCount < 1)
            {
                limits.CanRollback = true;
                limits.RemainingRollbackCount = 1 - ticket.RollbackCount;
                // 不设置时间限制，设置为一个很大的值表示无限制
                limits.RollbackTimeRemainingMilliseconds = long.MaxValue;
            }

            return limits;
        }
    }

    // 状态切换验证结果
    public class StatusChangeValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
