import 'package:intl/intl.dart';

/// 时区工具类 - 专门处理马来西亚时区（UTC+8）
class TimezoneUtils {
  // 马来西亚时区偏移量（UTC+8）
  static const int malaysiaOffsetHours = 8;
  
  /// 将UTC时间转换为马来西亚时间
  static DateTime toMalaysiaTime(DateTime utcDateTime) {
    // 确保输入是UTC时间
    final utc = utcDateTime.isUtc ? utcDateTime : utcDateTime.toUtc();
    // 添加8小时偏移量
    return utc.add(Duration(hours: malaysiaOffsetHours));
  }
  
  /// 格式化为马来西亚时间的日期时间字符串
  static String formatDateTime(DateTime dateTime) {
    final malaysiaTime = toMalaysiaTime(dateTime);
    return '${malaysiaTime.year}-${malaysiaTime.month.toString().padLeft(2, '0')}-${malaysiaTime.day.toString().padLeft(2, '0')} ${malaysiaTime.hour.toString().padLeft(2, '0')}:${malaysiaTime.minute.toString().padLeft(2, '0')}';
  }
  
  /// 格式化为马来西亚时间的日期字符串
  static String formatDate(DateTime dateTime) {
    final malaysiaTime = toMalaysiaTime(dateTime);
    return '${malaysiaTime.year}-${malaysiaTime.month.toString().padLeft(2, '0')}-${malaysiaTime.day.toString().padLeft(2, '0')}';
  }
  
  /// 格式化为相对时间（几分钟前、几小时前等）
  static String formatRelativeTime(DateTime dateTime) {
    // 获取当前本地时间
    final nowLocal = DateTime.now();

    // 将输入的UTC时间转换为本地时间进行比较
    final inputLocal = dateTime.isUtc ? dateTime.toLocal() : dateTime;

    final difference = nowLocal.difference(inputLocal);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inMinutes >= 60) {
      // 如果超过60分钟，显示小时
      final hours = (difference.inMinutes / 60).floor();
      return '${hours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
  
  /// 使用intl包格式化时间（支持更复杂的格式）
  static String formatWithPattern(DateTime dateTime, String pattern) {
    final malaysiaTime = toMalaysiaTime(dateTime);
    final formatter = DateFormat(pattern);
    return formatter.format(malaysiaTime);
  }
  
  /// 获取当前马来西亚时间
  static DateTime nowInMalaysia() {
    final utcNow = DateTime.now().toUtc();
    return toMalaysiaTime(utcNow);
  }
  
  /// 调试用：显示时间转换信息
  static void debugTimeConversion(DateTime originalTime) {
    print('=== 时间转换调试 ===');
    print('原始时间: $originalTime');
    print('是否UTC: ${originalTime.isUtc}');
    print('转换为UTC: ${originalTime.toUtc()}');
    print('转换为本地: ${originalTime.toLocal()}');
    print('马来西亚时间: ${toMalaysiaTime(originalTime)}');
    print('格式化结果: ${formatDateTime(originalTime)}');
    print('相对时间: ${formatRelativeTime(originalTime)}');
    print('==================');
  }
}
