{"@@locale": "en", "appTitle": "IT Asset Manager", "appSubtitle": "Asset Management System", "welcomeBack": "Welcome back, ", "pleaseLoginToYourAccount": "Please login to your account", "username": "Username", "usernameHint": "Please enter username", "password": "Password", "passwordHint": "Please enter password", "login": "<PERSON><PERSON>", "loggingIn": "Logging in...", "loginSuccess": "Login Successful", "welcomeBackMessage": "Welcome back!", "secureLogin": "<PERSON><PERSON>", "encryptedTransmission": "Encrypted Data Transmission", "version": "IT Asset Manager v2.0", "pleaseEnterUsername": "Please enter username", "pleaseEnterPassword": "Please enter password", "passwordMinLength": "Password must be at least 6 characters", "userNotExists": "User does not exist, please check username", "passwordIncorrect": "Password is incorrect, please re-enter", "accountDisabled": "Account has been disabled, please contact administrator", "loginFailed": "<PERSON><PERSON> failed", "networkTimeout": "Network connection timeout, please check network connection", "serverError": "Server error, please try again later", "networkError": "Network connection failed, please check network settings", "requestCancelled": "Request cancelled", "unknownError": "Unknown error", "switchLanguage": "中文", "@switchLanguage": {"description": "Text shown on language switch button to switch to Chinese"}, "validationError": "Validation error", "requestError": "Request error", "permissionDenied": "Permission denied, please contact administrator", "resourceNotFound": "The requested resource does not exist", "dataConflict": "Data conflict, please refresh and try again", "dataValidationFailed": "Data validation failed", "serviceUnavailable": "Service temporarily unavailable, please try again later", "requestFailed": "Request failed, please try again", "dashboard": "Dashboard", "assetManagement": "Assets", "ticketManagement": "Tickets", "activityLog": "Activity", "userManagement": "Users", "createAsset": "Create Asset", "editAsset": "Edit Asset", "assetDetails": "Asset Details", "ticketDetails": "Ticket Details", "assignTicket": "Assign <PERSON>", "createUser": "Create User", "editUser": "Edit User", "userDetails": "User Details", "itAssetManagement": "IT Asset Management", "role": "Role", "unknown": "Unknown", "user": "User", "assetOverview": "Asset Overview", "totalAssets": "Total Assets", "retired": "Retired", "maintenance": "Maintenance", "unassigned": "Unassigned", "assigned": "Assigned", "userOverview": "User Overview", "totalUsers": "Total Users", "administrators": "Admin", "regularUsers": "Users", "assetDistribution": "Asset Distribution", "quickActions": "Quick Actions", "viewAssets": "View Assets", "addAsset": "Add <PERSON>set", "viewActivityLog": "View Activity Log", "loadFailed": "Load Failed", "retry": "Retry", "noData": "No Data Available", "confirmLogout": "Confirm <PERSON>ut", "confirmLogoutMessage": "Are you sure you want to logout?", "cancel": "Cancel", "logout": "Logout", "logoutTooltip": "Logout", "statusDistribution": "Status Distribution", "categoryDistribution": "Category Distribution", "myWorkspace": "My Workspace", "myAssets": "My Assets", "createTicket": "Create Ticket", "userProfile": "User Profile", "workspace": "Workspace", "confirmBatchDelete": "Confirm Batch Delete", "confirmBatchDeleteMessage": "Are you sure you want to delete the selected {count} assets?\nThis action cannot be undone.", "@confirmBatchDeleteMessage": {"placeholders": {"count": {"type": "int"}}}, "delete": "Delete", "deletingAssets": "Deleting {count} assets...", "@deletingAssets": {"placeholders": {"count": {"type": "int"}}}, "batchDeleteSuccess": "Batch Delete Successful", "batchDeleteSuccessMessage": "Successfully deleted {count} assets", "@batchDeleteSuccessMessage": {"placeholders": {"count": {"type": "int"}}}, "deleteCompleted": "Delete completed: {successCount} successful, {failCount} failed", "@deleteCompleted": {"placeholders": {"successCount": {"type": "int"}, "failCount": {"type": "int"}}}, "filterConditions": "Filter Conditions", "clear": "Clear", "assetCategory": "Asset Category", "allCategories": "All Categories", "assetStatus": "Asset Status", "allStatuses": "All Statuses", "applyFilter": "Apply Filter", "searchAssetsHint": "Search asset name, number or description...", "exportExcel": "Export Excel", "filter": "Filter", "batchSelect": "Batch Select", "selectedItems": "{count} items selected", "@selectedItems": {"placeholders": {"count": {"type": "int"}}}, "pleaseSelectAssets": "Please select assets", "selectAll": "Select All", "deselectAll": "Deselect All", "deleteSelected": "Delete Selected", "loadingAssets": "Loading asset data...", "reload": "Reload", "categoryLaptop": "Laptop", "categoryDesktop": "Desktop", "categoryMonitor": "Monitor", "categoryPrinter": "Printer", "categoryMobile": "Mobile Phone", "categoryTablet": "Tablet", "categoryServer": "Server", "categoryNetwork": "Network", "categoryOther": "Other", "statusUnassigned": "Unassigned", "statusAssigned": "Assigned", "statusMaintenance": "Maintenance", "statusRetired": "Retired", "confirmDelete": "Confirm Delete", "confirmDeleteAsset": "Are you sure you want to delete asset \"{name}\"? This action cannot be undone.", "@confirmDeleteAsset": {"placeholders": {"name": {"type": "String"}}}, "deleteSuccess": "Delete Successful", "assetDeletedSuccess": "Asset deleted successfully", "deleteFailed": "Delete failed: {error}", "@deleteFailed": {"placeholders": {"error": {"type": "String"}}}, "notSet": "Not Set", "assetNotExists": "Asset does not exist", "basicInfo": "Basic Information", "assetNumber": "Number", "category": "Category", "brand": "Brand", "model": "Model", "serialNumber": "Serial Number", "financialInfo": "Financial Information", "value": "Value", "purchaseDate": "Purchase Date", "vendor": "<PERSON><PERSON><PERSON>", "assignmentInfo": "Assignment Information", "assignedTo": "Assigned to: {name}", "location": "Location", "maintenanceInfo": "Maintenance Information", "lastMaintenanceDate": "Last Maintenance Date", "nextMaintenanceDate": "Next Maintenance Date", "systemInfo": "System Information", "createdAt": "Created At", "updatedAt": "Updated At", "description": "Description", "loadingAssetInfo": "Loading asset information...", "loadUserListFailed": "Failed to load user list: {error}", "@loadUserListFailed": {"placeholders": {"error": {"type": "String"}}}, "assetNotFound": "Asset not found", "loadAssetFailed": "Failed to load asset: {error}", "@loadAssetFailed": {"placeholders": {"error": {"type": "String"}}}, "updateSuccess": "Update Successful", "createSuccess": "Create Successful", "assetUpdatedSuccess": "Asset information updated successfully", "assetCreatedSuccess": "New asset created successfully", "updateAssetFailed": "Failed to update asset", "createAssetFailed": "Failed to create asset", "operationFailed": "Operation failed: {error}", "@operationFailed": {"placeholders": {"error": {"type": "String"}}}, "assetName": "Asset Name", "assetNameHint": "Please enter asset name", "assetNumberHint": "Auto-generated by system", "status": "Status", "brandHint": "Please enter brand name", "modelHint": "Please enter model", "serialNumberHint": "Please enter serial number", "valueHint": "Please enter asset value", "vendorHint": "Please enter vendor name", "assignedUserHint": "Select assigned user", "locationHint": "Please enter location information", "descriptionHint": "Please enter asset description", "save": "Save", "saving": "Saving...", "pleaseEnterAssetName": "Please enter asset name", "pleaseEnterBrand": "Please enter brand", "pleaseEnterModel": "Please enter model", "pleaseEnterSerialNumber": "Please enter serial number", "pleaseEnterValidValue": "Please enter valid value", "pleaseSelectPurchaseDate": "Please select purchase date", "selectDate": "Select date", "pleaseEnter": "Please enter", "pleaseEnterVendor": "Please enter vendor", "pleaseEnterLocation": "Please enter location", "detailedInfo": "Detailed Information", "financialDetails": "Financial Details", "assignmentDetails": "Assignment Details", "maintenanceDetails": "Maintenance Details", "additionalInfo": "Additional Information", "recordCount": "{count} records found", "@recordCount": {"placeholders": {"count": {"type": "int"}}}, "noAssetsFound": "No matching assets found", "noAssetsData": "No asset data available", "adjustSearchCriteria": "Try adjusting search criteria", "addFirstAsset": "Click the button above to add your first asset", "exportingExcel": "Exporting Excel file...", "exportSuccess": "Export Successful", "exportSuccessMessage": "Excel file saved to: {path}", "@exportSuccessMessage": {"placeholders": {"path": {"type": "String"}}}, "exportFailed": "Export failed: {error}", "@exportFailed": {"placeholders": {"error": {"type": "String"}}}, "selectSaveLocation": "Select Save Location", "selectExcelSaveLocation": "Please select the save location for Excel file:", "appDirectory": "App Directory", "downloadFolder": "Download Folder", "edit": "Edit", "initializing": "Initializing...", "ticketProgress": "Ticket Progress", "problemDescription": "Problem Description", "commentRecords": "Comment Records", "adminOperations": "Admin Operations", "currentAssignee": "Current Assignee", "notAssigned": "Not Assigned", "manualAssignToOtherAdmin": "Manually Assign to Other Administrator", "statusPending": "Pending", "statusInProgress": "In Progress", "statusResolved": "Resolved", "statusClosed": "Closed", "priorityLow": "Low", "priorityMedium": "Medium", "priorityHigh": "High", "categoryHardware": "Hardware", "categorySoftware": "Software", "categoryAccount": "Account", "iWillHandle": "I'll Handle This", "iHaveResolved": "I Have Resolved", "iWillClose": "I'll Close This", "assignTicketDialog": "Assign <PERSON>", "selectAdminToAssign": "Select administrator to assign:", "ticketAssigned": "Ticket has been assigned", "assignmentFailed": "Assignment failed: {error}", "@assignmentFailed": {"placeholders": {"error": {"type": "String"}}}, "statusUpdated": "Status Updated", "ticketAutoAssignedToYou": "Ticket has been automatically assigned to you", "statusUpdateFailed": "Status update failed", "closedTicketCannotChangeStatus": "Closed tickets cannot change status", "rollbackChanceUsed": "Rollback chance has been used, cannot rollback again", "currentStatusNotAllowClose": "Current status does not allow closing", "loginExpiredPleaseRelogin": "<PERSON><PERSON> has expired, please log in again", "networkFailedCheckAndRetry": "Network connection failed, please check network and retry", "addReply": "Add Reply", "enterReplyContent": "Enter reply content...", "sendReply": "Send Reply", "replyHasBeenSent": "Reply has been sent", "sendFailed": "Send failed: {error}", "@sendFailed": {"placeholders": {"error": {"type": "String"}}}, "manageMyComments": "Manage My Comments", "commentDeleted": "Comment deleted", "batchDeletedComments": "Deleted {count} comments in total", "@batchDeletedComments": {"placeholders": {"count": {"type": "int"}}}, "batchDeleteFailed": "Batch delete failed: {error}", "@batchDeleteFailed": {"placeholders": {"error": {"type": "String"}}}, "noComments": "No comments yet", "showAllComments": "Show all {count} comments", "@showAllComments": {"placeholders": {"count": {"type": "int"}}}, "collapseComments": "Collapse Comments", "me": "Me", "createdTime": "Created Time", "updatedTime": "Updated Time", "assignedTime": "Assigned Time", "resolvedTime": "Resolved Time", "ticketNotExists": "Ticket does not exist", "loadingFailed": "Loading failed", "getUserInfoFailed": "Failed to get user information", "ticketStatus": "Ticket Status", "priority": "Priority", "ticketCategory": "Ticket Category", "allPriorities": "All Priorities", "clearFilter": "Clear Filter", "clearAll": "Clear All", "searchTicketHint": "Search ticket number, title, username...", "totalRecords": "Total {count} records", "@totalRecords": {"placeholders": {"count": {"type": "int"}}}, "noTickets": "No tickets", "noTicketsUnderCurrentFilter": "No tickets found under current filter conditions", "@assignedTo": {"placeholders": {"name": {"type": "String"}}}, "commentsCount": "{count} comments", "@commentsCount": {"placeholders": {"count": {"type": "int"}}}, "submittedBy": "Submitted By", "closeConfirmation": "Closing ticket requires confirmation", "rollbackConfirmation": "Rollback operation requires confirmation", "directStatusUpdate": "Other statuses update directly", "autoAssignToCurrentAdmin": "Update ticket status and automatically assign to current administrator"}