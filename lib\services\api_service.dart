import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/user.dart';
import '../models/asset.dart';
import '../models/dashboard_stats.dart';
import '../models/activity_log.dart';
import 'mock_api_service.dart';
import 'package:intl/intl.dart';

class ApiService {
  // 根据平台选择不同的基础URL
  static String get baseUrl {
    if (kIsWeb) {
      // Web端使用localhost
      return 'http://localhost:5000/api';
    } else {
      // 移动端使用********（Android模拟器访问宿主机的特殊IP）
      return 'http://********:5000/api';
    }
  }
  
  static const bool useMockData = false; // 设置为true使用模拟数据，false使用真实API
  
  // 单例模式
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();
  
  late Dio _dio;
  String? _token;

  // 初始化方法，只在第一次调用时执行
  bool _isInitialized = false;
  
  // 对外暴露dio实例和token
  Dio get dio {
    _initialize();
    return _dio;
  }
  
  String? get token {
    return _token;
  }
  
  void _initialize() {
    if (_isInitialized) return;
    
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 10), // 增加超时时间
      receiveTimeout: const Duration(seconds: 10),
    ));

    // 日志拦截器已禁用以减少调试输出

    // 添加拦截器处理token
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        if (_token != null) {
          options.headers['Authorization'] = 'Bearer $_token';
        }
        handler.next(options);
      },
      onResponse: (response, handler) {
        handler.next(response);
      },
      onError: (error, handler) {
        if (error.response?.statusCode == 401) {
          // Token过期或无效，清除本地存储
          _clearToken();
        }
        handler.next(error);
      },
    ));
    
    _isInitialized = true;
  }

  // 初始化token
  Future<void> initializeToken() async {
    _initialize(); // 确保ApiService已初始化
    final prefs = await SharedPreferences.getInstance();
    _token = prefs.getString('auth_token');
  }

  // 保存token
  Future<void> _saveToken(String token) async {
    _initialize(); // 确保ApiService已初始化
    // 立即设置到内存变量中，确保后续请求能立即使用
    _token = token;
    
    // 异步保存到SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
  }

  // 清除token
  Future<void> _clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    _token = null;
  }

  // 登录
  Future<LoginResponse> login(String username, String password) async {
    _initialize(); // 确保ApiService已初始化
    
    if (useMockData) {
      try {
        final loginResponse = await MockApiService.login(username, password);
        await _saveToken(loginResponse.token);
        return loginResponse;
      } catch (e) {
        throw e.toString();
      }
    }

    try {
      final response = await _dio.post('/auth/login', data: {
        'username': username,
        'password': password,
      });

      final loginResponse = LoginResponse.fromJson(response.data);
      await _saveToken(loginResponse.token);
      return loginResponse;
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 登出
  Future<void> logout() async {
    await _clearToken();
  }

  // 获取当前用户信息
  Future<User> getCurrentUser() async {
    if (useMockData) {
      try {
        return await MockApiService.getCurrentUser(_token ?? '');
      } catch (e) {
        throw e.toString();
      }
    }

    try {
      final response = await _dio.get('/auth/me');
      return User.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 获取用户列表
  Future<List<User>> getUsers() async {
    _initialize(); // 确保ApiService已初始化
    
    if (useMockData) {
      try {
        return await MockApiService.getUsers();
      } catch (e) {
        throw e.toString();
      }
    }

    try {
      final response = await _dio.get('/auth/users');
      final List<dynamic> data = response.data;
      final users = data.map((json) => User.fromJson(json)).toList();
      return users;
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 获取资产列表
  Future<List<Asset>> getAssets({
    int page = 1,
    int limit = 20,
    String? search,
    List<String>? categories,
    List<String>? statuses,
  }) async {
    if (useMockData) {
      try {
        return await MockApiService.getAssets(
          page: page,
          limit: limit,
          search: search,
          categories: categories,
          statuses: statuses,
        );
      } catch (e) {
        throw e.toString();
      }
    }

    try {
      // 手动构建查询字符串以支持数组参数
      final queryParts = <String>[];
      queryParts.add('page=$page');
      queryParts.add('limit=$limit');

      if (search != null && search.isNotEmpty) {
        queryParts.add('search=${Uri.encodeComponent(search)}');
      }

      // 为每个分类添加单独的参数
      if (categories != null && categories.isNotEmpty) {
        for (final category in categories) {
          queryParts.add('category=${Uri.encodeComponent(category)}');
        }
      }

      // 为每个状态添加单独的参数
      if (statuses != null && statuses.isNotEmpty) {
        for (final status in statuses) {
          queryParts.add('status=${Uri.encodeComponent(status)}');
        }
      }

      final queryString = queryParts.join('&');
      final url = '/assets?$queryString';

      final response = await _dio.get(url);
      final List<dynamic> data = response.data['assets'] ?? response.data;
      return data.map((json) => Asset.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 获取单个资产
  Future<Asset> getAsset(String id) async {
    if (useMockData) {
      try {
        return await MockApiService.getAsset(id);
      } catch (e) {
        throw e.toString();
      }
    }

    try {
      final response = await _dio.get('/assets/$id');
      return Asset.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 创建资产
  Future<Asset> createAsset(Asset asset) async {
    _initialize(); // 确保ApiService已初始化
    
    try {
      final response = await _dio.post('/assets', data: asset.toJson());
      return Asset.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 更新资产
  Future<Asset> updateAsset(String id, Asset asset) async {
    try {
      final jsonData = asset.toJson();
      final originalStatus = jsonData['status'];
      
      final response = await _dio.put('/assets/$id', data: jsonData);
      final updatedAsset = Asset.fromJson(response.data);
      
      // 如果后端返回的状态与我们发送的不一致，再次更新状态
      if (response.data['status'] != originalStatus) {
        try {
          // 尝试使用专门的状态更新API
          final statusUpdateResponse = await _dio.patch('/assets/$id/status', data: {
            'status': originalStatus,
          });
          
          return Asset.fromJson(statusUpdateResponse.data);
        } catch (statusError) {
          // 如果专门的API不存在，尝试用PUT但只发送必要字段
          try {
            final minimalUpdateResponse = await _dio.put('/assets/$id', data: {
              'name': updatedAsset.name,
              'assetNumber': updatedAsset.assetNumber,
              'category': jsonData['category'],
              'status': originalStatus,
              // 不发送分配信息，避免后端根据分配信息修改状态
            });
            
            return Asset.fromJson(minimalUpdateResponse.data);
          } catch (minimalError) {
            // 如果都失败了，返回原始结果
            return updatedAsset;
          }
        }
      }
      
      return updatedAsset;
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 删除资产
  Future<void> deleteAsset(String id) async {
    try {
      await _dio.delete('/assets/$id');
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 获取仪表板统计数据
  Future<DashboardStats> getDashboardStats() async {
    if (useMockData) {
      try {
        return await MockApiService.getDashboardStats();
      } catch (e) {
        throw e.toString();
      }
    }

    try {
      // 获取资产统计数据
      final statsResponse = await _dio.get('/dashboard/stats');
      final statsData = statsResponse.data;
      
      // 获取用户统计数据
      Map<String, dynamic> userStats = {};
      try {
        final userResponse = await _dio.get('/users');
                 if (userResponse.data is List) {
           final users = userResponse.data as List;
           final totalUsers = users.length;
           final adminUsers = users.where((u) => 
               u['role']?.toString().toLowerCase().contains('admin') == true).length;
           final regularUsers = totalUsers - adminUsers; // 总用户数减去管理员数
           
           userStats = {
             'totalUsers': totalUsers,
             'adminUsers': adminUsers,
             'regularUsers': regularUsers,
           };
         } else if (userResponse.data is Map && userResponse.data['users'] is List) {
           final users = userResponse.data['users'] as List;
           final totalUsers = users.length;
           final adminUsers = users.where((u) => 
               u['role']?.toString().toLowerCase().contains('admin') == true).length;
           final regularUsers = totalUsers - adminUsers; // 总用户数减去管理员数
           
           userStats = {
             'totalUsers': totalUsers,
             'adminUsers': adminUsers,
             'regularUsers': regularUsers,
           };
         }
      } catch (e) {

        // 如果获取用户统计失败，使用默认值
        userStats = {
          'totalUsers': 0,
          'adminUsers': 0,
          'regularUsers': 0,
        };
      }
      
      // 合并资产统计和用户统计数据
      final combinedData = Map<String, dynamic>.from(statsData);
      combinedData.addAll(userStats);
      
      return DashboardStats.fromJson(combinedData);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 获取下一个资产编号
  Future<String> getNextAssetNumber(AssetCategory category) async {
    _initialize(); // 确保ApiService已初始化
    
    try {
      final categoryString = _categoryToBackendFormat(category);
      final response = await _dio.get('/assets/next-number/$categoryString');
      return response.data['nextAssetNumber'] ?? _generateDefaultAssetNumber(category);
    } catch (e) {
      // 如果API失败（包括404），使用默认生成逻辑
      return _generateDefaultAssetNumber(category);
    }
  }
  
  String _generateDefaultAssetNumber(AssetCategory category) {
    final prefix = _getCategoryPrefix(category);
    final now = DateTime.now();
    final year = now.year;
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');
    final randomNum = (DateTime.now().millisecondsSinceEpoch % 999 + 1).toString().padLeft(3, '0');
    return '$prefix$year$month$day$randomNum';
  }
  
  String _categoryToBackendFormat(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return 'Laptop';
      case AssetCategory.desktop:
        return 'Desktop';
      case AssetCategory.monitor:
        return 'Monitor';
      case AssetCategory.printer:
        return 'Printer';
      case AssetCategory.phone:
        return 'Phone';
      case AssetCategory.tablet:
        return 'Tablet';
      case AssetCategory.server:
        return 'Server';
      case AssetCategory.network:
        return 'Network';
      case AssetCategory.other:
        return 'Other';
    }
  }

  String _getCategoryPrefix(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return 'NB';
      case AssetCategory.desktop:
        return 'PC';
      case AssetCategory.monitor:
        return 'MON';
      case AssetCategory.printer:
        return 'PRT';
      case AssetCategory.phone:
        return 'PHN';
      case AssetCategory.tablet:
        return 'TAB';
      case AssetCategory.server:
        return 'SVR';
      case AssetCategory.network:
        return 'NET';
      case AssetCategory.other:
        return 'OTH';
    }
  }

  // 错误消息映射
  String _mapErrorMessage(String serverMessage, BuildContext? context) {
    if (context == null) return serverMessage;
    
    final l10n = AppLocalizations.of(context);
    if (l10n == null) return serverMessage;
    
    // 映射常见的服务器错误消息
    final errorMappings = {
      '用户不存在，请检查用户名': l10n.userNotExists,
      '密码错误，请重新输入': l10n.passwordIncorrect,
      '账户已被封禁，请联系管理员': l10n.accountDisabled,
      '用户名或密码错误': l10n.loginFailed,
      '验证错误': l10n.validationError,
      '请求错误': l10n.requestError,
      '权限不足，请联系管理员': l10n.permissionDenied,
      '请求的资源不存在': l10n.resourceNotFound,
      '数据冲突，请刷新后重试': l10n.dataConflict,
      '数据验证失败': l10n.dataValidationFailed,
      '服务器内部错误，请稍后重试': l10n.serverError,
      '服务暂时不可用，请稍后重试': l10n.serviceUnavailable,
      '请求失败，请重试': l10n.requestFailed,
    };
    
    return errorMappings[serverMessage] ?? serverMessage;
  }

  // 错误处理
  String _handleError(dynamic error, [BuildContext? context]) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return '网络连接超时，请检查网络连接';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final responseData = error.response?.data;
          
          // 获取服务器返回的错误消息
          String message = '服务器错误';
          if (responseData is Map && responseData.containsKey('message')) {
            message = responseData['message'];
          }
          
          // 根据状态码返回用户友好的错误消息
          switch (statusCode) {
            case 400:
              // 详细显示400错误的验证信息
              if (responseData is Map && responseData.containsKey('errors')) {
                final errors = responseData['errors'] as Map;
                final errorMessages = <String>[];
                errors.forEach((field, messages) {
                  if (messages is List) {
                    for (var msg in messages) {
                      errorMessages.add('$field: $msg');
                    }
                  }
                });
                return '验证错误:\n${errorMessages.join('\n')}';
              }
              return message.isNotEmpty ? message : '请求参数错误';
                         case 401:
               // 登录相关错误，映射为本地化消息
               final mappedMessage = _mapErrorMessage(message, context);
               return mappedMessage.isNotEmpty ? mappedMessage : 
                   (context != null ? AppLocalizations.of(context)?.loginFailed ?? '用户名或密码错误' : '用户名或密码错误');
            case 403:
              return '权限不足，请联系管理员';
            case 404:
              return '请求的资源不存在';
            case 409:
              return '数据冲突，请刷新后重试';
            case 422:
              return message.isNotEmpty ? message : '数据验证失败';
            case 500:
              return '服务器内部错误，请稍后重试';
            case 502:
            case 503:
            case 504:
              return '服务暂时不可用，请稍后重试';
            default:
              return message.isNotEmpty ? message : '请求失败，请重试';
          }
        case DioExceptionType.cancel:
          return '请求已取消';
        case DioExceptionType.unknown:
          return '网络连接失败，请检查网络设置';
        default:
          return '未知错误';
      }
    }
    return error.toString();
  }

  // 获取活动日志列表
  Future<ActivityLogListResponse> getActivityLogs({
    int page = 1,
    int limit = 20,
    String? search,
    dynamic activityType, // 支持单个类型或类型列表
    int? assetId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      
      // 处理活动类型参数，支持单个类型或多个类型
      if (activityType != null) {
        print('API Service - 接收到的activityType: $activityType, 类型: ${activityType.runtimeType}');
        if (activityType is String && activityType.isNotEmpty) {
          queryParams['activityType'] = activityType;
          print('API Service - 作为字符串传递: $activityType');
        } else if (activityType is List<String> && activityType.isNotEmpty) {
          // 如果是多个类型，将它们作为逗号分隔的字符串传递
          final joinedTypes = activityType.join(',');
          queryParams['activityType'] = joinedTypes;
          print('API Service - 作为列表传递，合并后: $joinedTypes');
        }
      }
      if (assetId != null) {
        queryParams['assetId'] = assetId;
      }
      if (startDate != null) {
        // 只发送日期部分，格式为 YYYY-MM-DD
        final startDateString = DateFormat('yyyy-MM-dd').format(startDate);
        queryParams['startDate'] = startDateString;
      }
      if (endDate != null) {
        // 只发送日期部分，格式为 YYYY-MM-DD
        final endDateString = DateFormat('yyyy-MM-dd').format(endDate);
        queryParams['endDate'] = endDateString;
      }
      final response = await _dio.get('/ActivityLogs', queryParameters: queryParams);
      return ActivityLogListResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 获取所有活动日志（用于导出）
  Future<List<ActivityLog>> getAllActivityLogs({
    String? search,
    dynamic activityType, // 支持单个类型或类型列表
    int? assetId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': 1,
        'limit': 10000, // 设置一个较大的限制来获取所有数据
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      
      // 处理活动类型参数，支持单个类型或多个类型
      if (activityType != null) {
        if (activityType is String && activityType.isNotEmpty) {
          queryParams['activityType'] = activityType;
        } else if (activityType is List<String> && activityType.isNotEmpty) {
          // 如果是多个类型，将它们作为逗号分隔的字符串传递
          queryParams['activityType'] = activityType.join(',');
        }
      }
      if (assetId != null) {
        queryParams['assetId'] = assetId;
      }
      if (startDate != null) {
        // 只发送日期部分，格式为 YYYY-MM-DD
        final startDateString = DateFormat('yyyy-MM-dd').format(startDate);
        queryParams['startDate'] = startDateString;
      }
      if (endDate != null) {
        // 只发送日期部分，格式为 YYYY-MM-DD
        final endDateString = DateFormat('yyyy-MM-dd').format(endDate);
        queryParams['endDate'] = endDateString;
      }

      final response = await _dio.get('/ActivityLogs', queryParameters: queryParams);
      final result = ActivityLogListResponse.fromJson(response.data);
      return result.activityLogs;
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 导出资产到Excel
  Future<void> exportAssetsToExcel({
    String? search,
    List<String>? categories,
    List<String>? statuses,
  }) async {
    try {
      // 手动构建查询字符串以支持数组参数
      final queryParts = <String>[];

      if (search != null && search.isNotEmpty) {
        queryParts.add('search=${Uri.encodeComponent(search)}');
      }

      // 为每个分类添加单独的参数
      if (categories != null && categories.isNotEmpty) {
        for (final category in categories) {
          queryParts.add('category=${Uri.encodeComponent(category)}');
        }
      }

      // 为每个状态添加单独的参数
      if (statuses != null && statuses.isNotEmpty) {
        for (final status in statuses) {
          queryParts.add('status=${Uri.encodeComponent(status)}');
        }
      }

      final queryString = queryParts.isNotEmpty ? '?${queryParts.join('&')}' : '';
      final url = '/assets/export$queryString';

      final response = await _dio.get(
        url,
        options: Options(responseType: ResponseType.bytes),
      );

      // 这里需要处理文件下载，具体实现取决于平台
      // 在Flutter Web中，可以使用dart:html的download功能
      // 在移动端，可以保存到文件系统
      throw UnimplementedError('Excel导出功能需要在具体平台实现');
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 导出活动日志到Excel
  Future<void> exportActivityLogsToExcel({
    String? search,
    String? activityType,
    int? assetId,
  }) async {
    try {
      final queryParams = <String, dynamic>{};

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (activityType != null && activityType.isNotEmpty) {
        queryParams['activityType'] = activityType;
      }
      if (assetId != null) {
        queryParams['assetId'] = assetId;
      }

      final response = await _dio.get(
        '/ActivityLogs/export',
        queryParameters: queryParams,
        options: Options(responseType: ResponseType.bytes),
      );

      // 这里需要处理文件下载，具体实现取决于平台
      throw UnimplementedError('Excel导出功能需要在具体平台实现');
    } catch (e) {
      throw _handleError(e);
    }
  }

  // 检查是否已登录
  bool get isLoggedIn => _token != null;
}
