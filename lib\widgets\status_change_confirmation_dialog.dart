import 'package:flutter/material.dart';

class StatusChangeConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final VoidCallback onConfirm;
  final VoidCallback? onCancel;
  final Color? confirmColor;
  final IconData? icon;
  final Widget? additionalContent;

  const StatusChangeConfirmationDialog({
    Key? key,
    required this.title,
    required this.message,
    required this.confirmText,
    this.cancelText = '取消',
    required this.onConfirm,
    this.onCancel,
    this.confirmColor,
    this.icon,
    this.additionalContent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: confirmColor ?? Theme.of(context).primaryColor,
              size: 28,
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            message,
            style: const TextStyle(fontSize: 16),
          ),
          if (additionalContent != null) ...[
            const SizedBox(height: 16),
            additionalContent!,
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: onCancel ?? () => Navigator.of(context).pop(),
          child: Text(
            cancelText,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            onConfirm();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: confirmColor ?? Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: Text(
            confirmText,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}

class RollbackConfirmationDialog extends StatelessWidget {
  final VoidCallback onConfirm;
  final int remainingCount;
  final Duration timeRemaining;

  const RollbackConfirmationDialog({
    Key? key,
    required this.onConfirm,
    required this.remainingCount,
    required this.timeRemaining,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StatusChangeConfirmationDialog(
      title: '确认回溯操作',
      message: '您确定要将工单状态从"已解决"回溯到"处理中"吗？',
      confirmText: '确认回溯',
      confirmColor: Colors.orange,
      icon: Icons.undo,
      onConfirm: onConfirm,
      additionalContent: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange[200]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning_amber,
                  color: Colors.orange[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '回溯限制提醒',
                  style: TextStyle(
                    color: Colors.orange[800],
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '• 剩余回溯机会：$remainingCount 次',
              style: TextStyle(
                color: Colors.orange[700],
                fontSize: 13,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '使用后将无法再次回溯，请谨慎操作！',
              style: TextStyle(
                color: Colors.red[600],
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CloseConfirmationDialog extends StatelessWidget {
  final VoidCallback onConfirm;

  const CloseConfirmationDialog({
    Key? key,
    required this.onConfirm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StatusChangeConfirmationDialog(
      title: '确认关闭工单',
      message: '您确定要关闭此工单吗？',
      confirmText: '确认关闭',
      confirmColor: Colors.grey[600],
      icon: Icons.close,
      onConfirm: onConfirm,
      additionalContent: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red[200]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Colors.red[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '重要提醒',
                  style: TextStyle(
                    color: Colors.red[800],
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '工单关闭后将无法再次修改状态，用户和管理员都只能查看，无法进行任何操作。',
              style: TextStyle(
                color: Colors.red[700],
                fontSize: 13,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '请确保问题已完全解决！',
              style: TextStyle(
                color: Colors.red[600],
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
